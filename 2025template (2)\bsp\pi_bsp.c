#include "pi_bsp.h"
#include "pid.h"
#include "mydefine.h"
#include "step_motor_bsp.h"
#include <stdlib.h>  // for abs function

// Ĭ��ֵ��Ϊ��Ч״̬��X, Y Ϊ 0
LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};
LaserCoord_t latest_green_laser_coord = {GREEN_LASER_ID, 0, 0, 0};

// PID������ʵ��
PID_T pid_x; // X��PID������
PID_T pid_y; // Y��PID������

// PID�����ṹ��
typedef struct
{
    float kp;          // ����ϵ��
    float ki;          // ����ϵ��
    float kd;          // ΢��ϵ��
    float sample_time; // ����ʱ��
    float out_min;     // ������Сֵ
    float out_max;     // ������ֵ
    float i_min;       // ���ֵ���Сֵ
    float i_max;       // ���ֵ���ֵ
    float deadzone;    // ������С
} PidParams_t;

PidParams_t pid_params_x = {
    .kp = 2.0f,   // ����Kp�����Ӧ�ٶ�
    .ki = 0.008f, // ���ӻ������С��̬���
    .kd = 0.015f, // ����΢��������ȶ���
    .sample_time = 0.01f,
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 1 // ��С������С����߾���
};

PidParams_t pid_params_y = {
    .kp = 2.0f,   // ����Kp�����Ӧ�ٶ�
    .ki = 0.008f, // ���ӻ������С��̬���
    .kd = 0.008f, // ����΢��������ȶ���
    .sample_time = 0.01f,
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 1 // ��С������С����߾���
};

// PIDĿ������
int target_x = 320; // Ĭ����Ļ����
int target_y = 240; // Ĭ����Ļ����

// ��ǰʵ������
int current_x = 320;
int current_y = 240;

// PID����״̬��־
static bool pid_running = false;

// ������ֵ
int8_t motor_x, motor_y;

// ���Ա�־����ʾ������Ϣ
#define DEBUG_PID 0

// ���ٱ��ʹ�����
#define CONSTRAIN(x, min, max) ((x) < (min) ? (min) : ((x) > (max) ? (max) : (x)))

/**
 * @brief ��������ص�����
 * ������ͷ��⵽��������ʱ����
 * @param coord ��������ṹ��
 */
void pid_laser_coord_callback(LaserCoord_t coord)
{
    // ֻ������ɫ�������꣨�ɸ�����Ҫ�޸�Ϊ������ɫ���⣩
    if (coord.type == RED_LASER_ID)
    {
        // ���µ�ǰλ��
        app_pid_update_position(coord.x, coord.y);
    }
    else if(coord.type == GREEN_LASER_ID)
    {
        app_pid_set_target(coord.x, coord.y);
    }
}

// MaixCam ���ݽ���������������ʽ��red:(x,y)\n �� gre:(x,y)\n
// �����������ֱ�Ӹ���ȫ�ֱ��� latest_red_laser_coord �� latest_green_laser_coord
int pi_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // ��ָ����

    int parsed_x, parsed_y; // ��ʱ�������ڴ洢��������X,Y����
    int parsed_count;
    LaserCoord_t coord;

    // ����ƥ�� "red:(x,y)" ��ʽ
    if (strncmp(buffer, "red:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2) // ���������X��Y����ֵ
            return -2; // ����ʧ��

        // �����ɹ�������ȫ�ֺ�ɫ��������
        latest_red_laser_coord.x = parsed_x;
        latest_red_laser_coord.y = parsed_y;
        latest_red_laser_coord.isValid = 1; // �������Ϊ��Ч

        // ��ӡ������Ϣ
		my_printf(&huart1, "Parsed RED: X=%d, Y=%d\r\n", latest_red_laser_coord.x, latest_red_laser_coord.y);

        // ���ûص�����
        coord.type = RED_LASER_ID;
        coord.x = parsed_x;
        coord.y = parsed_y;
        pid_laser_coord_callback(coord);
    }
    // ����ƥ�� "gre:(x,y)" ��ʽ
    else if (strncmp(buffer, "gre:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2) // ���������X��Y����ֵ
            return -2; // ����ʧ��

        // �����ɹ�������ȫ����ɫ��������
        latest_green_laser_coord.x = parsed_x;
        latest_green_laser_coord.y = parsed_y;
        latest_green_laser_coord.isValid = 1; // �������Ϊ��Ч

        // ��ӡ������Ϣ
		my_printf(&huart1, "Parsed GRE: X=%d, Y=%d\r\n", latest_green_laser_coord.x, latest_green_laser_coord.y);

        // ���ûص�����
        coord.type = GREEN_LASER_ID;
        coord.x = parsed_x;
        coord.y = parsed_y;
        pid_laser_coord_callback(coord);
    }
    // ֧��study�е�app_maixcam��ʽ
    else if (strncmp(buffer, "to:(", 4) == 0)
    {
        // �� "to:(" ֮��ʼ�������ڴ� %d,%d)
        parsed_count = sscanf(buffer + 4, "%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count == 2)
        {
            coord.type = RED_LASER_ID; // ӳ�� "to" ����Ϊ��ɫ����ID
            coord.x = parsed_x;
            coord.y = parsed_y;
            pid_laser_coord_callback(coord);
            return 0; // �ɹ�����
        }
    }
    // ֧��study�е�app_maixcam��ʽ
    else if (strncmp(buffer, "pur:(", 5) == 0)
    {
        // �� "pur:(" ֮��ʼ�������ڴ� %d,%d)
        parsed_count = sscanf(buffer + 5, "%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count == 2)
        {
            coord.type = GREEN_LASER_ID; // ӳ�� "pur" ����Ϊ��ɫ����ID
            coord.x = parsed_x;
            coord.y = parsed_y;
            pid_laser_coord_callback(coord);
            return 0; // �ɹ�����
        }
    }
    else
    {
        // �Ȳ��� "red:" Ҳ���� "gre:" ��ͷ����Ϊ��δ֪��ʽ����Ч����
        return -3; // δ֪����Ч��ʽ
    }

    return 0; // �ɹ�
}



/**
 * @brief ����PID��������ʼ��
 */
void app_pid_init(void)
{
    // ��ʼ��X��PID������
    pid_init(&pid_x,
             pid_params_x.kp, pid_params_x.ki, pid_params_x.kd,
             (float)current_x, pid_params_x.out_max);

    // ��ʼ��Y��PID������
    pid_init(&pid_y,
             pid_params_y.kp, pid_params_y.ki, pid_params_y.kd,
             (float)current_y, pid_params_y.out_max);

    // �Զ�����PID����
#if DEBUG_PID
    app_pid_start();
#endif
}

/**
 * @brief ����PID������Ŀ������
 * @param x X��Ŀ������
 * @param y Y��Ŀ������
 */
void app_pid_set_target(int x, int y)
{
#if DEBUG_PID
    my_printf(&huart1, "����Ŀ��λ��: X=%d, Y=%d\n", x, y);
#endif
    target_x = x;
    target_y = y;

    // ʹ�����ǵ�PID�м������Ŀ��ֵ
    pid_set_target(&pid_x, (float)target_x);
    pid_set_target(&pid_y, (float)target_y);
}

/**
 * @brief ���µ�ǰʵ������
 * @param x X�ᵱǰ����
 * @param y Y�ᵱǰ����
 */
void app_pid_update_position(int x, int y)
{
    // ���������Ч��
    if (x < 0 || x > 1920 || y < 0 || y > 1080)
    {
        return; // ��Ч����ֱ�Ӷ���
    }

    current_x = x;
    current_y = y;
}

/**
 * @brief �����޷�����
 * @param pid PID������
 * @param min ��Сֵ
 * @param max ���ֵ
 */
static void app_pid_limit_integral(PID_T *pid, float min, float max)
{
    if (pid->integral > max)
    {
        pid->integral = max;
    }
    else if (pid->integral < min)
    {
        pid->integral = min;
    }
}

/**
 * @brief ����PID�������
 * ͨ��PID�㷨�������ٶȿ���ֵ
 */
void app_pid_calc(void)
{
    int error_x, error_y;
    float output_x, output_y;

    // ����X�����
    error_x = target_x - current_x;
    error_y = target_y - current_y;

#if DEBUG_PID
    my_printf(&huart1, "���X=%d Y=%d\n", error_x, error_y);
#endif

    // ֻ��ͬʱ�������ڲ�ֹͣ���
    if (abs(error_x) <= pid_params_x.deadzone && abs(error_y) <= pid_params_y.deadzone)
    {
#if DEBUG_PID
        my_printf(&huart1, "�������ڣ�ֹͣ���\n");
#endif
        Step_Motor_Stop();
        return;
    }

    // ʹ��λ��ʽPID����X�����
    output_x = pid_calculate_positional(&pid_x, (float)current_x);

    // ִ�л����޷�
    app_pid_limit_integral(&pid_x, pid_params_x.i_min, pid_params_x.i_max);

    // ʹ��λ��ʽPID����Y�����
    output_y = pid_calculate_positional(&pid_y, (float)current_y);

    // ִ�л����޷�
    app_pid_limit_integral(&pid_y, pid_params_y.i_min, pid_params_y.i_max);

    // �޷�����
    output_x = CONSTRAIN(output_x, pid_params_x.out_min, pid_params_x.out_max);
    output_y = CONSTRAIN(output_y, pid_params_y.out_min, pid_params_y.out_max);

    // ��ȡ���յ������ֵ
    motor_x = (int8_t)output_x;
    motor_y = (int8_t)output_y;

#if DEBUG_PID
    my_printf(&huart1, "PID���: X=%d, Y=%d\n", motor_x, motor_y);
#endif

    // ���Ƶ��
    Motor_Set_Speed(motor_x, -motor_y);
}

/**
 * @brief ����PID����
 */
void app_pid_start(void)
{
    // ���PID�����Ѿ������У�ֱ�ӷ���
    if (pid_running)
    {
        return;
    }

#if DEBUG_PID
    my_printf(&huart1, "����PID����\n");
#endif

    // ����PID������
    pid_reset(&pid_x);
    pid_reset(&pid_y);

    // ����PID���б�־
    pid_running = true;
}

/**
 * @brief ֹͣPID����
 */
void app_pid_stop(void)
{
    // ���PID������ֹͣ��ֱ�ӷ���
    if (!pid_running)
    {
        return;
    }

#if DEBUG_PID
    my_printf(&huart1, "ֹͣPID����\n");
#endif

    // ֹͣ���
    Step_Motor_Stop();

    // ���PID���б�־
    pid_running = false;
}

void pi_proc(void)
{
    // ���PID������������ִ��PID����
    if (pid_running)
    {
        app_pid_calc();
    }
}

