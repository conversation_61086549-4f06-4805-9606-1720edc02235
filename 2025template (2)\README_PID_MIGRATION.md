# PID控制系统移植说明

## 概述
本次移植将study文件夹中的PID控制逻辑成功集成到主项目中，实现了激光跟踪的PID控制功能。

## 移植内容

### 1. 新增文件
- `app/pid.c` - PID算法核心实现（直接从study/pid.c复制）
- `app/pid.h` - PID算法头文件（直接从study/pid.h复制）
- `app/mydefine.c` - 通用定义和简化定时器实现
- `app/mydefine.h` - 通用定义头文件

### 2. 修改文件
- `bsp/pi_bsp.c` - 集成PID控制逻辑和激光坐标处理
- `bsp/pi_bsp.h` - 添加PID相关函数声明
- `app/mypid.c` - 添加PID系统初始化调用
- `bsp/bsp_system.h` - 添加新头文件包含

## 功能特性

### PID控制器
- 支持位置式和增量式PID算法
- 可配置的PID参数（Kp, Ki, Kd）
- 输出限幅和积分限幅功能
- 死区控制，减少抖动

### 激光跟踪
- 支持红色激光点检测（当前位置）
- 支持绿色激光点检测（目标位置）
- 兼容多种数据格式：
  - `red:(x,y)` / `gre:(x,y)` - 原有格式
  - `to:(x,y)` / `pur:(x,y)` - study格式兼容

### 电机控制
- 自动调用步进电机控制函数
- 支持双轴（X/Y）独立控制
- 死区内自动停止，避免无意义抖动

## 使用方法

### 初始化
系统启动时会自动调用 `app_pid_init()` 初始化PID控制器。

### 设置目标位置
```c
app_pid_set_target(320, 240);  // 设置目标位置为屏幕中心
```

### 更新当前位置
```c
app_pid_update_position(x, y);  // 更新当前检测到的位置
```

### 启动/停止控制
```c
app_pid_start();  // 启动PID控制
app_pid_stop();   // 停止PID控制
```

## 配置参数

### PID参数（可在pi_bsp.c中调整）
```c
// X轴PID参数
.kp = 2.0f,      // 比例系数
.ki = 0.008f,    // 积分系数  
.kd = 0.015f,    // 微分系数
.deadzone = 1    // 死区大小

// Y轴PID参数
.kp = 2.0f,      // 比例系数
.ki = 0.008f,    // 积分系数
.kd = 0.008f,    // 微分系数
.deadzone = 1    // 死区大小
```

### 输出限制
- 输出范围：-99 到 +99
- 积分项限制：-80 到 +80

## 工作流程

1. **数据接收**：通过串口接收激光坐标数据
2. **数据解析**：`pi_parse_data()` 解析坐标信息
3. **回调处理**：`pid_laser_coord_callback()` 处理坐标数据
   - 红色激光点 → 更新当前位置
   - 绿色激光点 → 设置目标位置
4. **PID计算**：`app_pid_calc()` 计算控制输出
5. **电机控制**：调用 `Motor_Set_Speed()` 控制步进电机

## 调试功能

通过设置 `DEBUG_PID` 宏为1可启用调试输出：
```c
#define DEBUG_PID 1  // 在pi_bsp.c中修改
```

调试信息包括：
- 目标位置设置
- 误差计算结果
- PID输出值
- 死区状态

## 注意事项

1. **坐标系统**：默认支持1920x1080分辨率，可根据实际需要调整
2. **电机方向**：Y轴输出取反（-motor_y），可根据机械结构调整
3. **采样频率**：PID控制在 `pi_proc()` 中以20ms周期执行
4. **参数调优**：根据实际系统响应调整PID参数
5. **类型定义**：`LaserCoord_t`结构体统一定义，避免重复声明错误

## 修复记录

### v1.1 - 类型重复定义修复
- 修复了`LaserCoord_t`在`pi_bsp.h`和`mydefine.h`中重复定义的编译错误
- 统一使用`#ifndef LASERCOORD_T_DEFINED`保护宏
- 确保结构体定义一致性，包含`isValid`字段

## 兼容性

- 保持与原有mypid.c系统的兼容性
- 支持原有的激光数据格式
- 新增study格式的兼容支持
- 不影响现有的电机控制功能
