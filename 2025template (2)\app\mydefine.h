// Copyright (c) 2024 米醋电子工作室

#ifndef __MYDEFINE_H
#define __MYDEFINE_H

#include "bsp_system.h"

// 激光点类型标识符
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'

// 激光坐标数据结构
#ifndef LASERCOORD_T_DEFINED
#define LASERCOORD_T_DEFINED
typedef struct {
    char type;    // 激光类型: 'R'表示红色激光，'G'表示绿色激光
    int x;        // X坐标
    int y;        // Y坐标
} LaserCoord_t;
#endif

// 简化的定时器结构体（替代MultiTimer）
typedef struct {
    uint32_t period;
    uint32_t last_tick;
    void (*callback)(void *userData);
    void *userData;
    uint8_t active;
} SimpleTimer_t;

// 简化的定时器函数
void simpleTimerStart(SimpleTimer_t *timer, uint32_t period, void (*callback)(void *userData), void *userData);
void simpleTimerStop(SimpleTimer_t *timer);
void simpleTimerUpdate(SimpleTimer_t *timer);

// 串口打印函数声明
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

#endif /* __MYDEFINE_H */
