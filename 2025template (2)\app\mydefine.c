// Copyright (c) 2024 米醋电子工作室

#include "mydefine.h"

/**
 * @brief 启动简化定时器
 * @param timer 定时器指针
 * @param period 周期(ms)
 * @param callback 回调函数
 * @param userData 用户数据
 */
void simpleTimerStart(SimpleTimer_t *timer, uint32_t period, void (*callback)(void *userData), void *userData)
{
    if (timer == NULL) return;
    
    timer->period = period;
    timer->last_tick = HAL_GetTick();
    timer->callback = callback;
    timer->userData = userData;
    timer->active = 1;
}

/**
 * @brief 停止简化定时器
 * @param timer 定时器指针
 */
void simpleTimerStop(SimpleTimer_t *timer)
{
    if (timer == NULL) return;
    
    timer->active = 0;
}

/**
 * @brief 更新简化定时器（需要在主循环中调用）
 * @param timer 定时器指针
 */
void simpleTimerUpdate(SimpleTimer_t *timer)
{
    if (timer == NULL || !timer->active || timer->callback == NULL) return;
    
    uint32_t current_tick = HAL_GetTick();
    if (current_tick - timer->last_tick >= timer->period)
    {
        timer->last_tick = current_tick;
        timer->callback(timer->userData);
    }
}
